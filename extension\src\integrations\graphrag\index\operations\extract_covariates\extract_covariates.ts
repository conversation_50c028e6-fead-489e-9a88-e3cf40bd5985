/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the extract_covariates verb definition.
 */

import { DataFrame } from '../../../data-model/types';
import { PipelineCache } from '../../../cache/pipeline-cache';
import { WorkflowCallbacks } from '../../../callbacks/workflow-callbacks';
import { AsyncType } from '../../../config/enums';
import { LanguageModelConfig } from '../../../config/models/language-model-config';
import { ModelManager } from '../../../language-model/manager';
import { deriveFromRows } from '../../utils/derive-from-rows';
import { ClaimExtractor } from './claim-extractor';
import { Covariate, CovariateExtractionResult } from './typing';

const logger = console;

const DEFAULT_ENTITY_TYPES = ["organization", "person", "geo", "event"];

/**
 * Extract claims from a piece of text.
 */
export async function extractCovariates(
    input: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    column: string,
    covariateType: string,
    strategy?: Record<string, any>,
    asyncMode: AsyncType = AsyncType.AsyncIO,
    entityTypes?: string[],
    numThreads: number = 4
): Promise<DataFrame> {
    logger.debug("extract_covariates strategy=", strategy);

    const entityTypesToUse = entityTypes || DEFAULT_ENTITY_TYPES;
    const resolvedEntitiesMap: Record<string, string> = {};
    const strategyConfig = { ...strategy };

    async function runStrategy(row: Record<string, any>): Promise<any[]> {
        const text = row[column];
        const result = await runExtractClaims(
            text,
            entityTypesToUse,
            resolvedEntitiesMap,
            callbacks,
            cache,
            strategyConfig
        );

        return result.covariateData.map(item =>
            createRowFromClaimData(row, item, covariateType)
        );
    }

    const results = await deriveFromRows(
        input,
        runStrategy,
        callbacks,
        numThreads,
        asyncMode,
        "extract covariates progress: "
    );

    // Flatten results
    const flatResults = results.flat().filter(item => item != null);

    // Create columns from first result or default
    const columns = flatResults.length > 0
        ? Object.keys(flatResults[0])
        : ['id', 'covariate_type', 'description'];

    return {
        columns: columns,
        data: flatResults
    };
}

/**
 * Create a row from the claim data and the input row.
 */
function createRowFromClaimData(
    row: Record<string, any>,
    covariateData: Covariate,
    covariateType: string
): Record<string, any> {
    return {
        ...row,
        ...covariateData,
        covariate_type: covariateType
    };
}

/**
 * Run the Claim extraction chain.
 */
async function runExtractClaims(
    input: string | Iterable<string>,
    entityTypes: string[],
    resolvedEntitiesMap: Record<string, string>,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    strategyConfig: Record<string, any>
): Promise<CovariateExtractionResult> {
    const llmConfig = new LanguageModelConfig(strategyConfig.llm || {});
    const modelManager = new ModelManager();
    const llm = modelManager.getOrCreateChatModel({
        name: "extract_claims",
        modelType: llmConfig.type,
        config: llmConfig,
        callbacks: callbacks,
        cache: cache
    });

    const extractionPrompt = strategyConfig.extraction_prompt;
    const maxGleanings = strategyConfig.max_gleanings || 1;
    const tupleDelimiter = strategyConfig.tuple_delimiter;
    const recordDelimiter = strategyConfig.record_delimiter;
    const completionDelimiter = strategyConfig.completion_delimiter;

    const extractor = new ClaimExtractor({
        modelInvoker: llm,
        extractionPrompt: extractionPrompt,
        maxGleanings: maxGleanings,
        onError: (e, s, d) => {
            logger.error("Claim Extraction Error", e, { stack: s, details: d });
        }
    });

    const claimDescription = strategyConfig.claim_description;
    if (!claimDescription) {
        throw new Error("claim_description is required for claim extraction");
    }

    const inputArray = typeof input === 'string' ? [input] : Array.from(input);

    const results = await extractor.extract({
        inputText: inputArray,
        entitySpecs: entityTypes,
        resolvedEntities: resolvedEntitiesMap,
        claimDescription: claimDescription,
        tupleDelimiter: tupleDelimiter,
        recordDelimiter: recordDelimiter,
        completionDelimiter: completionDelimiter
    });

    const claimData = results.output;
    return {
        covariateData: claimData.map(createCovariate)
    };
}

/**
 * Create a covariate from the item.
 */
function createCovariate(item: Record<string, any>): Covariate {
    return {
        subjectId: item.subject_id,
        objectId: item.object_id,
        type: item.type,
        status: item.status,
        startDate: item.start_date,
        endDate: item.end_date,
        description: item.description,
        sourceText: item.source_text,
        recordId: item.record_id,
        id: item.id
    };
}