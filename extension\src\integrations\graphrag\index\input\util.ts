/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Shared column processing for structured input files.
 */

import { DataFrame } from '../../data-model/types';
import { InputConfig } from '../../config/models/input-config';
import { PipelineStorage } from '../../storage/pipeline-storage';
import { genSha512Hash } from '../utils/hashing';

const logger = console;

/**
 * Type for file loader function
 */
type LoaderFunction = (path: string, group?: Record<string, any>) => Promise<DataFrame>;

/**
 * Load files from storage and apply a loader function.
 * @param loader - Function to load individual files
 * @param config - Input configuration
 * @param storage - Pipeline storage instance
 * @returns Promise resolving to combined DataFrame
 */
export async function loadFiles(
    loader: LoaderFunction,
    config: InputConfig,
    storage: PipelineStorage,
): Promise<DataFrame> {
    // Create regex pattern for file matching
    const filePattern = new RegExp(config.filePattern);
    
    const files = Array.from(storage.find(filePattern, {
        fileFilter: config.fileFilter,
    }));

    if (files.length === 0) {
        throw new Error(`No ${config.fileType} files found in ${config.storage.baseDir}`);
    }

    const filesLoaded: DataFrame[] = [];

    for (const [file, group] of files) {
        try {
            const result = await loader(file, group);
            filesLoaded.push(result);
        } catch (e) {
            logger.warn(`Warning! Error loading file ${file}. Skipping...`);
            logger.warn(`Error: ${e}`);
        }
    }

    logger.info(
        `Found ${files.length} ${config.fileType} files, loading ${filesLoaded.length}`
    );

    // Combine all DataFrames
    const allColumns = new Set<string>();
    const allData: Record<string, any>[] = [];

    filesLoaded.forEach(df => {
        df.columns.forEach(col => allColumns.add(col));
        allData.push(...df.data);
    });

    const result: DataFrame = {
        columns: Array.from(allColumns),
        data: allData
    };

    logger.info(
        `Total number of unfiltered ${config.fileType} rows: ${result.data.length}`
    );
    
    return result;
}

/**
 * Process configured data columns of a DataFrame.
 * @param documents - DataFrame to process
 * @param config - Input configuration
 * @param path - File path for logging
 * @returns Processed DataFrame
 */
export function processDataColumns(
    documents: DataFrame,
    config: InputConfig,
    path: string
): DataFrame {
    const data = documents.data.map(row => {
        const newRow = { ...row };

        // Generate ID if not present
        if (!('id' in newRow)) {
            newRow.id = genSha512Hash(newRow, Object.keys(newRow));
        }

        // Handle text column
        if (config.textColumn && !('text' in newRow)) {
            if (!(config.textColumn in newRow)) {
                logger.warn(
                    `text_column ${config.textColumn} not found in csv file ${path}`
                );
            } else {
                newRow.text = newRow[config.textColumn];
            }
        }

        // Handle title column
        if (config.titleColumn) {
            if (!(config.titleColumn in newRow)) {
                logger.warn(
                    `title_column ${config.titleColumn} not found in csv file ${path}`
                );
            } else {
                newRow.title = newRow[config.titleColumn];
            }
        } else {
            newRow.title = path;
        }

        return newRow;
    });

    // Update columns to include any new columns
    const allColumns = new Set(documents.columns);
    data.forEach(row => {
        Object.keys(row).forEach(key => allColumns.add(key));
    });

    return {
        columns: Array.from(allColumns),
        data: data
    };
}